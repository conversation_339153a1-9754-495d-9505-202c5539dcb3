### 本地浏览器记录

位置: browser文件夹，使用electron框架打包的

20250814:
- [init] 从utools-plugins迁移过来

### docker发布记录

pugwoo/neatchat:20250826-6
- [modify] 首字延迟无论什么时候都记录和可显示
- [modify] 默认基准超时时间由5分钟改回1分钟，现在解决了重试问题了，而且一般也有没有超过1分钟了

pugwoo/neatchat:20250815
- [modify] maxToken默认值从4000改成16000
- [modify] 默认模型改成gpt-5-mini

pugwoo/neatchat:20250814
- [add] 增加显示原文的按钮

pugwoo/neatchat:20250813-2
- [modify] 默认模型改成o4-mini
- [modify] 快捷切换模型改成gpt-5-mini和o4-mini

pugwoo/neatchat:20250813
- [fix] 修复显示svg整个页面崩溃的问题

pugwoo/neatchat:20250812
- [add] 增加4o和gpt-5作为vision model
- [modify] 快捷切换模型改成gpt-5-mini和gpt-5
- [del] 去掉所有Azure模型
- [add] 增加gpt-5/gpt-5-mini/gpt-5-nano/gpt-5-thinking模型
- [modify] 默认模型改成gpt-5-mini
- [clean] 清理掉一些老的模型

pugwoo/neatchat:20250723
- [add] 美化文案做成环境变量

pugwoo/neatchat:20250722
- [modify] 默认模型从o1-mini切换成o4-mini
- [add] 增加美化输出的快捷按钮
- [add] 增加模型gpt-4o-image和o4-mini

pugwoo/neatchat:20250416
- [enhance] 尝试修复上传图片问题，不确定是否修复了
- [enhance] 指定压缩摘要的模型，解决自定义模型会修改摘要的模型的问题
- [enhance] 调整按钮样式

pugwoo/neatchat:20250415

- [enhance] 不自动滚动到底部
- [enhance] 默认紧边框，即聊天窗口最大化
- [enhance] 一直显示当前模型名称
- [enhance] 不显示左上角的neatchat图标
- [add] 增加模型快捷切换按钮
- [enhance] 默认基准超时时间由1分钟改成5分钟
