const CHATGPT_NEXT_WEB_CACHE = "chatgpt-next-web-cache";
const CHATGPT_NEXT_WEB_FILE_CACHE = "chatgpt-next-web-file";
let a="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let nanoid=(e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e));for(let n=0;n<e;n++)t+=a[63&r[n]];return t};

self.addEventListener("activate", function (event) {
  console.log("ServiceWorker activated.");
});

self.addEventListener("install", function (event) {
  self.skipWaiting();  // enable new version
  event.waitUntil(
    caches.open(CHATGPT_NEXT_WEB_CACHE).then(function (cache) {
      return cache.addAll([]);
    }),
  );
});

function jsonify(data) {
  return new Response(JSON.stringify(data), { headers: { 'content-type': 'application/json' } })
}

async function upload(request, url) {
  try {
    const formData = await request.formData()
    const file = formData.getAll('file')[0]

    // 验证文件是否存在
    if (!file) {
      console.error('[ServiceWorker] No file found in form data')
      return jsonify({ code: 1, error: 'No file provided' })
    }

    // 验证文件大小
    if (file.size === 0) {
      console.error('[ServiceWorker] Empty file detected')
      return jsonify({ code: 1, error: 'Empty file not allowed' })
    }

    // 安全地获取文件扩展名
    let ext = 'bin' // 默认扩展名
    if (file.name && file.name.includes('.')) {
      ext = file.name.split('.').pop()
    } else if (file.type) {
      // 从 MIME 类型获取扩展名
      const mimeExt = file.type.split('/').pop()
      if (mimeExt && mimeExt !== 'octet-stream') {
        ext = mimeExt
      }
    }

    // 处理 blob 类型
    if (ext === 'blob' && file.type) {
      ext = file.type.split('/').pop() || 'bin'
    }

    // 生成文件URL
    const fileUrl = `/api/cache/${nanoid()}.${ext}`
    console.debug('[ServiceWorker] Uploading file:', file.name, 'size:', file.size, 'type:', file.type, 'url:', fileUrl)

    // 尝试打开缓存
    const cache = await caches.open(CHATGPT_NEXT_WEB_FILE_CACHE)

    // 创建响应对象
    const response = new Response(file, {
      headers: {
        'content-type': file.type || 'application/octet-stream',
        'content-length': file.size.toString(),
        'cache-control': 'no-cache', // file already store in disk
        'server': 'ServiceWorker',
      }
    })

    // 尝试存储到缓存
    await cache.put(new Request(fileUrl), response)

    console.debug('[ServiceWorker] File uploaded successfully:', fileUrl)
    return jsonify({ code: 0, data: fileUrl })

  } catch (error) {
    console.error('[ServiceWorker] Upload failed:', error)
    return jsonify({
      code: 1,
      error: 'Upload failed: ' + (error.message || 'Unknown error')
    })
  }
}

async function remove(request, url) {
  try {
    const cache = await caches.open(CHATGPT_NEXT_WEB_FILE_CACHE)
    const res = await cache.delete(request.url)
    console.debug('[ServiceWorker] File removed:', request.url, 'success:', res)
    return jsonify({ code: 0, deleted: res })
  } catch (error) {
    console.error('[ServiceWorker] Remove failed:', error)
    return jsonify({
      code: 1,
      error: 'Remove failed: ' + (error.message || 'Unknown error')
    })
  }
}

self.addEventListener("fetch", (e) => {
  const url = new URL(e.request.url);
  if (/^\/api\/cache/.test(url.pathname)) {
    if ('GET' == e.request.method) {
      e.respondWith(
        caches.match(e.request).catch(error => {
          console.error('[ServiceWorker] Cache match failed:', error)
          return new Response('Cache error', { status: 500 })
        })
      )
    }
    if ('POST' == e.request.method) {
      e.respondWith(upload(e.request, url))
    }
    if ('DELETE' == e.request.method) {
      e.respondWith(remove(e.request, url))
    }
  }
});
